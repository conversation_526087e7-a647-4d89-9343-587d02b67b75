---
// Product benefits component to highlight value proposition
export interface Props {
  class?: string;
}

const { class: className = '' } = Astro.props;
---

<div class={`bg-gradient-to-br from-accent-50 to-primary-50 rounded-2xl p-6 ${className}`}>
  <h3 class="text-lg font-semibold text-primary-900 mb-4 flex items-center gap-2">
    <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
    </svg>
    Why Choose Our Digital Images?
  </h3>
  
  <div class="space-y-4">
    <div class="flex items-start gap-3">
      <div class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5">
        <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div>
        <h4 class="font-medium text-primary-900">Professional Quality</h4>
        <p class="text-sm text-primary-600">High-resolution images perfect for any project</p>
      </div>
    </div>
    
    <div class="flex items-start gap-3">
      <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5">
        <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div>
        <h4 class="font-medium text-primary-900">Commercial License</h4>
        <p class="text-sm text-primary-600">Use for personal and commercial projects</p>
      </div>
    </div>
    
    <div class="flex items-start gap-3">
      <div class="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0 mt-0.5">
        <svg class="w-3 h-3 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div>
        <h4 class="font-medium text-primary-900">Multiple Formats</h4>
        <p class="text-sm text-primary-600">Available in JPEG, PNG, and other formats</p>
      </div>
    </div>
    
    <div class="flex items-start gap-3">
      <div class="w-6 h-6 rounded-full bg-orange-100 flex items-center justify-center flex-shrink-0 mt-0.5">
        <svg class="w-3 h-3 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div>
        <h4 class="font-medium text-primary-900">24/7 Support</h4>
        <p class="text-sm text-primary-600">Get help whenever you need it</p>
      </div>
    </div>
  </div>
</div>
