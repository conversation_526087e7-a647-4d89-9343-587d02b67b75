---
// Customer testimonials component for social proof
export interface Props {
  class?: string;
}

const { class: className = '' } = Astro.props;

// Sample testimonials - in a real app, these would come from a CMS or API
const testimonials = [
  {
    name: "<PERSON>",
    role: "Graphic Designer",
    content: "Amazing quality images! Perfect for my client projects. The download was instant and the resolution is exactly what I needed.",
    rating: 5,
    avatar: "S<PERSON>"
  },
  {
    name: "<PERSON>",
    role: "Marketing Manager",
    content: "Great value for money. These images helped elevate our marketing campaigns. Highly recommend!",
    rating: 5,
    avatar: "MC"
  },
  {
    name: "<PERSON>",
    role: "Content Creator",
    content: "Love the variety and quality. The commercial license gives me peace of mind for my business use.",
    rating: 5,
    avatar: "ED"
  }
];
---

<div class={`bg-white rounded-2xl border border-primary-100 p-6 ${className}`}>
  <div class="flex items-center gap-2 mb-6">
    <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
    </svg>
    <h3 class="text-lg font-semibold text-primary-900">What Our Customers Say</h3>
  </div>
  
  <div class="space-y-4">
    {testimonials.map((testimonial) => (
      <div class="border-l-4 border-accent-200 pl-4 py-2">
        <div class="flex items-start gap-3">
          <div class="w-10 h-10 rounded-full bg-accent-100 flex items-center justify-center text-accent-700 font-semibold text-sm flex-shrink-0">
            {testimonial.avatar}
          </div>
          <div class="flex-1">
            <div class="flex items-center gap-1 mb-1">
              {Array.from({ length: testimonial.rating }).map((_, i) => (
                <svg key={i} class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
              ))}
            </div>
            <p class="text-sm text-primary-700 mb-2">"{testimonial.content}"</p>
            <div class="text-xs text-primary-500">
              <span class="font-medium">{testimonial.name}</span>
              <span class="mx-1">•</span>
              <span>{testimonial.role}</span>
            </div>
          </div>
        </div>
      </div>
    ))}
  </div>
  
  <div class="mt-6 pt-4 border-t border-primary-100 text-center">
    <div class="flex items-center justify-center gap-2 text-sm text-primary-600">
      <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
      </svg>
      <span>Join 1,000+ satisfied customers</span>
    </div>
  </div>
</div>
