case 'FAQPage':
  return {
    ...baseData,
    mainEntity: data.faqs.map(faq => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer
      }
    }))
  };


  image: data.images.map(img => ({
  "@type": "ImageObject",
  url: img,
  width: "1200",
  height: "900",
  caption: data.name
}))


case 'Article':
  return {
    ...baseData,
    headline: data.title,
    author: {
      "@type": "Person",
      name: data.author
    },
    datePublished: data.publishDate,
    dateModified: data.modifiedDate,
    publisher: {
      "@type": "Organization",
      name: "InfPik"
    }
  };