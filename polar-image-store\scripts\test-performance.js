// Performance testing script
// Run this in browser console to test performance improvements

function testPerformance() {
  console.log('🚀 Testing Performance Improvements...\n');

  // Test 1: Font loading
  console.log('1. Font Loading Test:');
  const fontFace = new FontFace('Inter', 'url(/fonts/inter.woff2)');
  fontFace.load().then(() => {
    console.log('✅ Inter font loaded successfully');
  }).catch(() => {
    console.log('❌ Inter font failed to load');
  });

  // Test 2: Service Worker
  console.log('\n2. Service Worker Test:');
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistration().then((registration) => {
      if (registration) {
        console.log('✅ Service Worker is registered');
        console.log('   Scope:', registration.scope);
      } else {
        console.log('❌ Service Worker is not registered');
      }
    });
  } else {
    console.log('❌ Service Worker not supported');
  }

  // Test 3: Cache API
  console.log('\n3. Cache API Test:');
  if ('caches' in window) {
    caches.keys().then((cacheNames) => {
      console.log('✅ Cache API available');
      console.log('   Cache names:', cacheNames);
    });
  } else {
    console.log('❌ Cache API not available');
  }

  // Test 4: Resource hints
  console.log('\n4. Resource Hints Test:');
  const preconnectLinks = document.querySelectorAll('link[rel="preconnect"]');
  const dnsPrefetchLinks = document.querySelectorAll('link[rel="dns-prefetch"]');
  const preloadLinks = document.querySelectorAll('link[rel="preload"]');
  
  console.log(`✅ Preconnect links: ${preconnectLinks.length}`);
  preconnectLinks.forEach(link => console.log(`   - ${link.href}`));
  
  console.log(`✅ DNS-prefetch links: ${dnsPrefetchLinks.length}`);
  dnsPrefetchLinks.forEach(link => console.log(`   - ${link.href}`));
  
  console.log(`✅ Preload links: ${preloadLinks.length}`);
  preloadLinks.forEach(link => console.log(`   - ${link.href} (${link.as})`));

  // Test 5: Image loading
  console.log('\n5. Image Loading Test:');
  const images = document.querySelectorAll('img');
  let lazyImages = 0;
  let eagerImages = 0;
  
  images.forEach(img => {
    if (img.loading === 'lazy') lazyImages++;
    if (img.loading === 'eager') eagerImages++;
  });
  
  console.log(`✅ Total images: ${images.length}`);
  console.log(`   - Lazy loading: ${lazyImages}`);
  console.log(`   - Eager loading: ${eagerImages}`);

  // Test 6: Performance metrics
  console.log('\n6. Performance Metrics:');
  if ('performance' in window) {
    const navigation = performance.getEntriesByType('navigation')[0];
    if (navigation) {
      console.log('✅ Navigation timing available:');
      console.log(`   - DNS lookup: ${navigation.domainLookupEnd - navigation.domainLookupStart}ms`);
      console.log(`   - TCP connection: ${navigation.connectEnd - navigation.connectStart}ms`);
      console.log(`   - Request: ${navigation.responseStart - navigation.requestStart}ms`);
      console.log(`   - Response: ${navigation.responseEnd - navigation.responseStart}ms`);
      console.log(`   - DOM loading: ${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`);
      console.log(`   - Total load time: ${navigation.loadEventEnd - navigation.navigationStart}ms`);
    }

    // Core Web Vitals
    if ('PerformanceObserver' in window) {
      console.log('\n7. Core Web Vitals:');
      
      // LCP (Largest Contentful Paint)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        console.log(`✅ LCP: ${lastEntry.startTime.toFixed(2)}ms`);
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // FID (First Input Delay) - only available after user interaction
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          console.log(`✅ FID: ${entry.processingStart - entry.startTime}ms`);
        });
      }).observe({ entryTypes: ['first-input'] });

      // CLS (Cumulative Layout Shift)
      let clsValue = 0;
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        console.log(`✅ CLS: ${clsValue.toFixed(4)}`);
      }).observe({ entryTypes: ['layout-shift'] });
    }
  }

  console.log('\n🎉 Performance test completed!');
  console.log('💡 Check Network tab for resource loading optimization');
  console.log('💡 Use Lighthouse for comprehensive performance audit');
}

// Auto-run test when script loads
testPerformance();
